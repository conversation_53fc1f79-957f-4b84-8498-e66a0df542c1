'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { campusLocations } from '@/lib/campus-data';
import { Camera } from 'lucide-react';

// 🔁 Dynamically import components to avoid SSR issues
const RealTimeCameraCapture = dynamic(() => import('@/components/RealTimeCameraCapture'), { ssr: false });
const LocationSelector = dynamic(() => import('@/components/LocationSelector'), { ssr: false });
const NavigationMap = dynamic(() => import('@/components/NavigationMap'), { ssr: false });

type LocationState = 'detection' | 'destination' | 'navigation';

export default function LocationDetection() {
  const [locationState, setLocationState] = useState<LocationState>('detection');
  const [currentLocation, setCurrentLocation] = useState<string | null>(null);
  const [destinationLocation, setDestinationLocation] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [detectionConfidence, setDetectionConfidence] = useState<number>(0);
  const { toast } = useToast();

  const handleLocationDetected = (location: string, confidence: number) => {
    setCurrentLocation(location);
    setDetectionConfidence(confidence);
    setLocationState('destination');
    setIsCameraOpen(false);

    toast({
      title: 'Location Detected!',
      description: `Found: ${location} (${Math.round(confidence * 100)}% confidence)`,
    });
  };

  const openCamera = () => {
    setIsCameraOpen(true);
  };

  const handleDestinationSelect = (location: string) => {
    setDestinationLocation(location);
  };

  const handleProceedToNavigation = () => {
    if (!destinationLocation) {
      toast({
        title: "Please select a destination",
        description: "You need to select where you want to go",
        variant: "destructive",
      });
      return;
    }
    setLocationState('navigation');
  };

  const resetProcess = () => {
    setLocationState('detection');
    setCurrentLocation(null);
    setDestinationLocation(null);
    setDetectionConfidence(0);
    setIsCameraOpen(false);
  };

  return (
    <section id="location-detection" className="py-16 container">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold tracking-tight text-center mb-8">
          Find Your Way Around Campus
        </h2>

        <Card className="w-full">
          <CardHeader>
            <CardTitle>Campus Navigation</CardTitle>
            <CardDescription>
              {locationState === 'detection' && "Use real-time camera to detect your current location"}
              {locationState === 'destination' && "Select where you want to go"}
              {locationState === 'navigation' && "Follow the route to your destination"}
            </CardDescription>
          </CardHeader>

          <CardContent>
            {locationState === 'detection' && (
              <div className="space-y-6">
                <div className="text-center">
                  <Button
                    onClick={openCamera}
                    size="lg"
                    className="w-full max-w-md"
                  >
                    <Camera className="h-5 w-5 mr-2" />
                    Start Real-time Detection
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    Camera will automatically detect your location every 5 seconds
                  </p>
                </div>
              </div>
            )}

            {locationState === 'destination' && (
              <div className="space-y-6">
                <div className="p-4 bg-muted rounded-lg mb-6">
                  <h3 className="font-medium mb-2">Your Current Location:</h3>
                  <p className="text-xl font-bold">{currentLocation}</p>
                  <div className="mt-2 text-sm text-muted-foreground">
                    Detection Confidence: {Math.round(detectionConfidence * 100)}%
                  </div>
                </div>

                <LocationSelector
                  locations={campusLocations.map(loc => loc.name)}
                  currentLocation={currentLocation || ''}
                  onSelect={handleDestinationSelect}
                  selectedLocation={destinationLocation}
                />
              </div>
            )}

            {locationState === 'navigation' && currentLocation && destinationLocation && (
              <div className="space-y-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-medium">From:</h3>
                    <p className="font-bold">{currentLocation}</p>
                  </div>
                  <div className="flex-1 text-right">
                    <h3 className="font-medium">To:</h3>
                    <p className="font-bold">{destinationLocation}</p>
                  </div>
                </div>

                <div className="h-[400px] w-full rounded-lg overflow-hidden border">
                  <NavigationMap
                    startLocation={currentLocation}
                    endLocation={destinationLocation}
                  />
                </div>
              </div>
            )}
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={resetProcess}>
              Start Over
            </Button>

            {locationState === 'destination' && (
              <Button onClick={handleProceedToNavigation}>
                Get Directions
              </Button>
            )}
          </CardFooter>
        </Card>

        {/* Real-time Camera Modal */}
        <RealTimeCameraCapture
          isOpen={isCameraOpen}
          onClose={() => setIsCameraOpen(false)}
          onLocationDetected={handleLocationDetected}
        />
      </div>
    </section>
  );
}
